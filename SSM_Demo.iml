<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/src/main/webapp/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/src/main/webapp" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/Java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration>
        <fileset id="fileset" name="Spring Servlet" removed="false">
          <file>file://$MODULE_DIR$/src/main/resources/spring-servlet.xml</file>
        </fileset>
      </configuration>
    </facet>
    <facet type="jpa" name="JPA">
      <configuration>
        <setting name="validation-enabled" value="true" />
        <datasource-mapping>
          <factory-entry name="Entities" />
          <factory-entry name="SSM_Demo" />
        </datasource-mapping>
        <naming-strategy-map />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_6">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/Java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/Test" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/Test/Java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-orm:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-test:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-mock:2.0.8" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:4.3.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:4.3.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:4.3.1.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper:3.7.3" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:0.9.1" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:5.1.38" level="project" />
    <orderEntry type="library" name="Maven: c3p0:c3p0:0.9.1.2" level="project" />
    <orderEntry type="library" name="Maven: jstl:jstl:1.2" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.3.1" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.4" level="project" />
    <orderEntry type="library" name="Maven: org.json:json:20160212" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils:1.8.3" level="project" />
    <orderEntry type="library" name="Maven: commons-collections:commons-collections:3.2.1" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.2" level="project" />
    <orderEntry type="library" name="Maven: net.sf.ezmorph:ezmorph:1.0.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.6.2" level="project" />
    <orderEntry type="library" name="Maven: net.iharder:base64:2.3.8" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-core:2.6.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.6.2" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains:annotations-java5:21.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: net.java.dev.jna:jna:4.1.0" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: javax.servlet:javax.servlet-api:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-frontend-jaxws:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: xml-resolver:xml-resolver:1.2" level="project" />
    <orderEntry type="library" name="Maven: asm:asm:3.3.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-api:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.woodstox:woodstox-core-asl:4.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.woodstox:stax2-api:3.1.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ws.xmlschema:xmlschema-core:2.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.geronimo.specs:geronimo-javamail_1.4_spec:1.7.1" level="project" />
    <orderEntry type="library" name="Maven: wsdl4j:wsdl4j:1.6.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-core:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-impl:2.1.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-bindings-soap:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-databinding-jaxb:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-bindings-xml:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-frontend-simple:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-ws-addr:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.cxf:cxf-rt-ws-policy:2.7.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.neethi:neethi:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.pdfbox:pdfbox-app:2.0.16" level="project" />
    <orderEntry type="library" name="Maven: org.apache.pdfbox:fontbox:2.0.12" level="project" />
  </component>
</module>