<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert.css" />
    <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />
</head>

<body>
    <div class="wrapper">
        <div class="header">
            <div id="logo" class="logo-baosight"></div>
            <div class="title">工贸一体机登记</div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li class="fontblue">1.读取身份证</li>
                    <li class="arrow"></li>
                    <li>2.选择装卸货</li>
                    <li class="arrow"></li>
                    <li>3.进厂登记</li>
                </ul>
            </div>
        </div>
        <div class="container">
            <div class="main">
                <div class="information">
                    <ul>
                        <li><span>身份证</span><input id="id_card" class="ipt1" placeholder="请输入您的身份证号码" autocomplete="off"></li>
                        <li><span>姓&nbsp;&nbsp;&nbsp;&nbsp;名</span><input id="name" class="ipt1" placeholder="请输入您的姓名" autocomplete="off"></li>
                        <li><span>厂&nbsp;&nbsp;&nbsp;&nbsp;区</span><input id="factory_id" class="ipt2" readonly="readonly" /></li>
                        <li id="li_phone"><span>手机号</span><input id="phone" class="ipt1" placeholder="" autocomplete="off"></li>
                    </ul>
                    <span cass="phone_tips">注意：如需排队提醒，可输入手机号</span>
                </div>
                <div class="btn">
                    <button id="indexRead" class="read" type="button">读取身份证</button>
                    <button id="indexNext" class="next" type="button" onclick="nextStep();">下一步
                </button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/sweetalert-dev.js"></script>
    <script type="text/javascript" src="js/virtualkeyboard.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script>
        var id_card;
        var name;
        var factory_id;

        //绑定输入法
        $(".ipt1").virtualkeyboard();

        var seg_no;
        window.onload = function() {
            seg_no = GetPar("seg_no");
            factory_id = GetPar("factory_id");
            $("#factory_id").val(factory_id);
        }

        $(document).ready(function() {
            //根据segNo显示对应logo
            switchLogo();
            var segNo = GetPar("seg_no");
            if (segNo == '00181') {
                $("#li_phone").css('display', 'block');
            } else {
                $("#li_phone").css('display', 'none');
            }
        });

        function GetPar(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }

        //下一步
        function nextStep() {
            id_card = $("#id_card").val();
            name = $("#name").val();
            if ("一厂" == $("#factory_id").val()) {
                factory_id = "F1";
            } else if ("二厂" == $("#factory_id").val()) {
                factory_id = "F2";
            } else if ("三水工厂" == $("#factory_id").val()) {
                factory_id = "F3";
            } else if ("天津宝钢" == $("#factory_id").val()) {
                factory_id = "F1";
            } else if ("天津宝井" == $("#factory_id").val()) {
                factory_id = "F1";
            } else {
                factory_id = "F1";
            }
            if (id_card == "" || id_card == null) {
                swal("请输入身份证号！", "", "warning");
                return false;

            }
            if (name == "" || name == null) {
                swal("请输入姓名！", "", "warning");
                return false;
            }

            console.log("url=" + ytjServerUrl + "queryVehicleAndZXH");
            console.log("下一步参数：id_card=" + id_card + "  name=" + name + "  seg_no=" + seg_no);

            $.ajax({
                url: ytjServerUrl + "queryVehicleAndZXH",
                type: "post",
                data: {
                    id_card: id_card,
                    name: name,
                    seg_no: seg_no
                },
                dataType: "jsonp", //指定服务器返回的数据类型
                success: function(res) {
                    console.log(res);
                    console.log(name);
                    localStorage.setItem("vehicle_id", res.vehicle_id);
                    localStorage.setItem("hand_big_type", res.hand_big_type);
                    localStorage.setItem("allocate_vehicle_id",
                        res.allocate_vehicle_id);
                    localStorage.setItem("id_card", id_card);
                    localStorage.setItem("seg_no", seg_no);
                    localStorage.setItem("factory_id", factory_id);
                    localStorage.setItem("name", name);

                    window.location.href = "next";
                }
            });
        }

        // 身份证读取 - 开启端口
        $("#indexRead").unbind("click").bind("click", function() {
            $.ajax({
                url: "ReadIdCard",
                type: "post",
                async: false,
                success: function(res) {
                    //返回状态： 1，身份证读取端口开启成功。
                    console.log(JSON.stringify(res));
                    if (res.status == "1") {
                        //数据查询
                        getIdentityInfo();
                    } else {
                        swal(res.statusDesc, "", "warning");
                        console.log(JSON.stringify(res));
                        return;
                    }
                }
            });
        });

        //获取身份证数据
        function getIdentityInfo() {
            $.ajax({
                url: "GetIdCard",
                type: "post",
                async: false,
                success: function(res) {
                    console.log(JSON.stringify(res));
                    //返回状态： 1，身份证读取成功。
                    if (res.status == "1") {
                        localStorage.setItem("id_card", res.param.strID); //身份证号
                        localStorage.setItem("name", res.param.strName); //身份证号
                        //关闭端口
                        swal(res.statusDesc, "", "success");
                        $("#id_card").val(res.param.strID);
                        $("#name").val(res.param.strName);
                        closeComm();
                    } else {
                        alert(JSON.stringify(res));
                        swal(res.statusDesc, "", "warning");
                        console.log(JSON.stringify(res));
                        return;
                    }
                }
            });
        }

        //关闭端口
        function closeComm() {
            $.ajax({
                url: "CloseIdCard",
                type: "post",
                async: false,
                success: function(res) {
                    console.log(JSON.stringify(res));
                    //返回状态： 1，身份证读取成功。
                    if (res.status == "0") {
                        alert(JSON.stringify(res));
                        swal(res.statusDesc, "", "warning");
                        console.log(JSON.stringify(res));
                        return;
                    }
                }
            });
        }
    </script>
</body>

</html>