<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
	<title>工贸一体机</title>
	<link rel="stylesheet" type="text/css" href="css/newstyle.css"/>
	<link rel="stylesheet" type="text/css" href="css/sweetalert.css"/>
	<link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css"/>

</head>

<body>
<div class="wrapper">
	<div class="header">
		<div id= "logo" class="logo-baosight"></div>
		<div class="title">工贸一体机登记</div>
	</div>
	<div class="nav">
		<div class="navbox">
			<ul>
				<li>1.读取身份证</li>
				<li class="arrow"></li>
				<li class="fontblue">2.选择装卸货</li>
				<li class="arrow"></li>
				<li>3.进厂登记</li>
			</ul>
		</div>
	</div>
	<div class="container">
		<div class="main">
			<div class="information2">
				<ul>
					<li><span>车牌号</span><input id="province" class="ipt"><input
							id="license" class="ipt" type="text"></li>
					<li><span>类型</span>
						<button id="loading" class="loading" value="10"
								onclick="selected(this.value)">装货
						</button>
						<button id="discharge" class="discharge" value="20"
								onclick="selected(this.value)">卸货
						</button>
					</li>
				</ul>
			</div>
			<div class="btn">
				<button class="before" onclick="javascript :history.back(-1);">上一步</button>
				<button class="next" onclick="nextStep();">下一步</button>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="js/sweetalert-dev.js"></script>
<script type="text/javascript" src="js/virtualkeyboard.js"></script>
<script type="text/javascript" src="js/config.js"></script>
<script>
	var vehicle_id = localStorage.getItem("vehicle_id");//车牌号
	var hand_big_type = localStorage.getItem("hand_big_type");//装卸货类型
	var seg_no = localStorage.getItem("seg_no");

	//绑定输入法
	$(".ipt").virtualkeyboard();

	$("#province").click(function () {
		$('.virtualkeyboard').show();
	});
	$("#license").click(function () {
		$('.virtualkeyboard').show();
	});

	//监听车牌键盘输入
	$('#license').bind('input propertychange', function () {
		var inputContent = $("#license").val();
		console.log("输入内容：" + inputContent);
		$("#license").val(inputContent.toUpperCase());
	});

	//页面加载将车牌号与装卸货填充
	window.onload = function () {
		if (vehicle_id != null && vehicle_id != ""
				&& vehicle_id != "undefined") {
			$("#province").val(vehicle_id.substring(0, 1));
			$("#license").val(vehicle_id.substring(1));
		}
		if (hand_big_type != null && hand_big_type != ""
				&& hand_big_type != "undefined") {
			selected(hand_big_type);
		}
	}

	$(document).ready(function () {
		//根据segNo显示对应logo
		switchLogo();
	});

	//  10 : 装货  ；20 ： 卸货
	function selected(value, css) {
		if (value == 10) {
			/* $(".loading").css("border", "3px solid #5b8cdd");
            $(".discharge").css("border",
                    "1px solid rgba(210, 210, 210, 1)"); */

			$(".loading").css("background-image",
					'url("images/typeactive.png")');
			$(".discharge").css("background-image",
					'url("images/typebg.png")');

		} else {
			/* $(".discharge").css("border", "3px solid #5b8cdd");
            $(".loading").css("border", "1px solid rgba(210, 210, 210, 1)"); */
			$(".discharge").css("background-image",
					'url("images/typeactive.png")');
			$(".loading").css("background-image",
					'url("images/typebg.png")');
		}

		//选择装卸货时，将装卸货类型保存
		hand_big_type = value;
		localStorage.setItem("hand_big_type", value);
	}

	//下一步
	function nextStep() {
		//车牌框不能为空
		if ($("#province").val() == "" || $("#license").val() == "") {
			swal("请输入车牌号！", "", "warning");
			return false;
		}
		var licenseVal = $("#license").val();
		console.log("当前segNo=" + seg_no);
		if ((seg_no == '00118'||seg_no == '00152') && (licenseVal.length < 6 || licenseVal.length > 7)) {
			swal("请输入正确的车牌号！", "", "warning");
			return false;
		}
		if (hand_big_type == null || hand_big_type == ""
				|| hand_big_type == "undefined") {
			swal("请选择装卸货类型！", "", "warning");
			return false;
		}
		//以防手动修改车牌号，在跳转下个页面时保存车牌号
		localStorage.setItem("vehicle_id", $("#province").val()
				+ $("#license").val());
		window.location.href = "nextStep";
	}
</script>
</body>

</html>