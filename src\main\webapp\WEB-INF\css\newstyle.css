@charset "UTF-8";

/* CSS Document */


/*base*/

html {
    height: 100%;
}

body {
    margin: 0;
    padding: 0;
    color: #333;
    font-size: 14px;
    font-family: Microsoft YaHei, sans-serif;
    background-color: #f4f4f4;
    background-image: url("../images/bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100%;
}

ul,
li {
    list-style: none;
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
}

p {
    padding: 0;
    margin: 0;
}


/**********************************************information1 begin*/

.wrapper {
    width: 100%;
}

.header {
    width: 100%;
    height: 100px;
    background: #e5ebf3;
}


/*.logo {*/


/*	width: 232px;*/


/*	height: 50px;*/


/*	background: url(../images/logo-baosight.png) no-repeat;*/


/*	margin: 25px 0 0 50px;*/


/*	float: left;*/


/*}*/

.logo-baosight {
    width: 272px;
    height: 60px;
    background: url(../images/logo-baosight.png) no-repeat;
    margin: 30px 0 0 0px;
    float: left;
}

.logo-ouye {
    width: 272px;
    height: 60px;
    background: url(../images/logo-ouye.png) no-repeat;
    margin: 15px 0 0 0px;
    float: left;
}

.title {
    font-size: 30px;
    height: 100px;
    line-height: 100px;
    margin: 0 50px 0 0;
    float: right
}

.nav {
    width: 100%;
    height: 80px;
    line-height: 80px;
    background: #203890;
    color: #fff;
    float: left;
}

.navbox {
    width: 1150px;
    margin: 0 auto;
}

.navbox li {
    width: 20%;
    height: 80px;
    float: left;
    display: block;
    font-size: 22px;
    text-align: center;
}

.fontblue {
    color: #aff;
}

.container {
    width: 1080px;
    margin: 140px auto;
    background-image: url("../images/bg.png")
}

.arrow {
    background: url(../images/arrow.png) no-repeat 50% 50%;
}

.main {
    width: 1080px;
    /*height: 250px;*/
    margin-top: -20px;
}

.information {
    float: left;
    /*height: 250px;*/
    background-image: url("../images/informationbg.jpg");
    background-repeat: no-repeat;
    padding: 10px 0;
    background-size: 100% 100%;
}

.information ul li {
    margin: 25px 30px;
    /*margin-bottom: 20px;*/
    /*margin-left: 10px;*/
}

.information span {
    float: left;
    width: 72px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}

.ipt1 {
    width: 430px;
    height: 50px;
    background-color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

.ipt2 {
    width: 430px;
    height: 50px;
    background-color: #d3d3d3;
    border: 1px solid #ffffff;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    border: 1px solid #ffffff;
}

.btn {
    width: 220px;
    height: 180px;
    float: right;
}

.btn button {
    width: 220px;
    height: 80px;
    background-image: url("../images/btnbg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    color: #fff;
    text-indent: 10px;
    font-family: Microsoft Yahei, sans-serif;
    cursor: pointer;
}

.btn button:active {
    background-image: url("../images/btnhover.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.next {
    margin-top: 30px;
}

.keyboard {
    width: 1080px;
    height: 320px;
    background-color: #c3d0e2;
    position: absolute;
    bottom: 30px;
}

.number {
    width: 1020px;
    height: 280px;
    margin: 20px 0 20px 40px;
}

.number ul li {
    float: left;
    width: 82px;
    height: 68px;
    line-height: 68px;
    background-image: url("../images/butbg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-align: center;
    margin-top: 2px;
    margin-right: 10px;
}

.number a {
    color: #ffffff;
    font-size: 20px;
}


/************************************************information1 end*/


/************************************************information2 begin*/

#province {
    width: 80px;
    height: 50px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(102, 102, 102, 1);
    border-radius: 4px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    text-align: center;
}

#license {
    width: 338px;
    height: 50px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(102, 102, 102, 1);
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    margin-left: 5px;
}

.loading {
    width: 213px;
    height: 50px;
    background-image: url("../images/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.loading:focus {
    background-image: url("../images/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.discharge {
    width: 213px;
    height: 50px;
    background-image: url("../images/typebg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #333;
    margin-left: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    border-radius: 4px;
}

.discharge:focus {
    background-image: url("../images/typeactive.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.information2 {
    float: left;
    height: 250px;
    background-image: url("../images/informationbg.jpg");
    background-repeat: no-repeat;
    padding: 55px 25px;
}

.information2 ul li {
    margin-bottom: 30px;
    margin-left: 10px;
}

.information2 span {
    float: left;
    width: 72px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}


/************************************************information2 end*/

.information-bill {
    float: left;
    background-image: url("../images/informationbg.jpg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 20px 35px;
}

.information-bill span {
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
}

.information-bill .tag {
    float: left;
    margin-right: 10px;
}


/************************************************information4 begin*/

.tips {
    float: right;
    width: 1080px;
    height: 30px;
    margin: -30px auto 10px;
}

.tips p {
    display: block;
    text-align: right;
    margin-right: -10px;
    font-size: 18px;
    font-weight: 400;
    color: #ecb627;
    line-height: 36px;
}


/************************************************information4 end*/


/************************************************information5 begin*/

i {
    font-style: normal;
}

.ibox {
    width: 1080px;
    height: 360px;
    background-color: #c3d0e2;
    padding: 40px;
}

.check {
    float: left;
}

.check p {
    font-size: 20px;
    color: #333333;
    margin-bottom: 30px;
}

.check i {
    margin-left: 30px;
}


/************************************************information5 end*/