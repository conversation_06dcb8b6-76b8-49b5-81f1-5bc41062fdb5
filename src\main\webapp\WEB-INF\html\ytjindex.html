<!doctype html>
<html>

<head>
    <meta charset="UTF-8">
    <title>一体机终端</title>
    <link rel="stylesheet" type="text/css" href="css/indexstyle.css" />
</head>

<body>
    <div class="top">
        <div id="logo" class="logo-baosight"></div>
    </div>
    <div class="content">
        <div class="entrance">
            <div class="sel">
                <div class="cho">
                    <a class="gongmao" href="registerHome?seg_no=00129&factory_id=一厂">
                        <button class="gm">进厂登记</button>
                    </a>
                    <a id="bill_print" class="dayin" href="billPrintInput">
                        <button id="bill_print_btn">自助打印提单</button>
                    </a>
                    <!-- 天津自助打印 调用iec页面 -->
                    <a id='putout_print' class="dayin" href="http://localhost:8080/bep-ytj/ytj/noLoginProcessPrintCheck.jsp?segNoType=tj">
                        <button id="putout_print_btn">自助打印</button>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript">
        var segNo = getYtjSegNoConfig();
        var factoryName = getYtjFactoryConfig();
        $(document).ready(function() {
            //根据segNo显示对应logo
            switchLogo();
            $(".gongmao").attr('href', 'registerHome?seg_no=' + segNo + '&factory_id=' + factoryName);
            if (segNo == '00126') {
                $("#putout_print").attr('href', 'ytjPrintIndex');
                $("#bill_print").css('display', 'none');
            } else if (segNo == '00181') {
                $("#putout_print").attr('href', 'ytjPrintIndex');
                $("#putout_print_btn").text("自助打印出库单")
                $("#bill_print").css('display', 'block');
            } else {
                $("#bill_print").css('display', 'none');
            }
        });
    </script>
</body>

</html>