<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="css/sweetalert.css">
</head>

<body>
    <div class="wrapper">
        <div class="header">
            <div id="logo" class="logo-baosight"></div>
            <div class="title">工贸一体机登记</div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li>1.读取身份证</li>
                    <li class="arrow"></li>
                    <li>2.选择装卸货</li>
                    <li class="arrow"></li>
                    <li class="fontblue">3.进厂登记</li>
                </ul>
            </div>
        </div>
        <div class="container">
            <div class="ibox">
                <div class="check">
                    <ul>
                        <li>
                            <p>
                                身份证：<i id="id_card"></i>
                            </p>
                        </li>
                        <li>
                            <p>
                                姓&nbsp;&nbsp;&nbsp;&nbsp;名：<i id="name"></i>
                            </p>
                        </li>
                        <li>
                            <p>
                                车&nbsp;&nbsp;&nbsp;&nbsp;牌：<i id="vehicle_id"></i>
                            </p>
                        </li>
                        <li>
                            <p>
                                类&nbsp;&nbsp;&nbsp;&nbsp;型：<i id="hand_big_type"></i>
                            </p>
                        </li>
                        <li>
                            <p>
                                厂&nbsp;&nbsp;&nbsp;&nbsp;区：<i id="factory_id"></i>
                            </p>
                        </li>
                    </ul>
                </div>
                <div class="btn">
                    <button class="read" onclick="javascript :history.back(-1);">上一步</button>
                    <button class="next" onclick="CheckIn()">入厂登记</button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/sweetalert-dev.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script>
        var id_card = localStorage.getItem("id_card");
        var name = localStorage.getItem("name");
        var vehicle_id = localStorage.getItem("vehicle_id").toUpperCase();
        var hand_big_type = localStorage.getItem("hand_big_type");
        var factory_id = localStorage.getItem("factory_id");
        var seg_no = localStorage.getItem("seg_no");
        var allocate_vehicle_id = localStorage.getItem("allocate_vehicle_id");

        $(document).ready(function() {
            //根据segNo显示对应logo
            switchLogo();
        });

        window.onload = function() {
            $("#id_card").html(id_card);
            $("#name").html(name);
            $("#vehicle_id").html(vehicle_id);
            console.log("进厂登记：seg_no=" + seg_no);
            //判定厂区代码
            $("#factory_id").html(getYtjFactoryConfig());
            // if (seg_no == "00129") {
            // 	$("#factory_id").html("天津宝钢");
            // } else if (seg_no == "00145") {
            // 	$("#factory_id").html("天津宝井");
            // } else if (seg_no == "00100") {
            // 	$("#factory_id").html("欧冶上海不锈");
            // } else if (factory_id == "F1") {
            // 	$("#factory_id").html("一厂");
            // } else if (factory_id == "F2") {
            // 	$("#factory_id").html("二厂");
            // } else if (factory_id == "F3") {
            // 	$("#factory_id").html("三水工厂");
            // }
            //判定装卸货类型
            if (hand_big_type == "10") {
                $("#hand_big_type").html("装货");
            } else {
                $("#hand_big_type").html("卸货");
            }
        }

        //入场登记
        function CheckIn() {
            $.ajax({
                //url : "http://localhost:8085/Demo/checkIn",
                //url : "http://***********/Demo-1.0-SNAPSHOT/checkIn",
                //url : "http://localhost:8081/Demo/checkIn",
                url: ytjServerUrl + "checkIn",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    id_card: id_card,
                    name: name,
                    seg_no: seg_no,
                    vehicle_id: vehicle_id,
                    factory_id: factory_id,
                    hand_big_type: hand_big_type,
                    allocate_vehicle_id: allocate_vehicle_id
                },
                success: function(res) {
                    console.log(res);
                    if ("1" == res.out_result) {
                        //成功登记后跳转首页
                        swal({
                            title: "登记成功",
                            type: "success"
                        }, function(isConfirm) {
                            if (isConfirm) {
                                window.location.href = "ytjindex";
                            }
                        });

                    } else {
                        swal(res.out_result_desc, "", "warning");
                        return;
                    }
                }
            });
        }
    </script>
</body>

</html>