package Entity;

import java.util.HashMap;

public class ResultInfo {
	private String status;
	private String statusDesc;
	private String desc;
	private HashMap<String, String> param;

	public ResultInfo(String status, String statusDesc, String desc) {
		this.status = status;
		this.statusDesc = statusDesc;
		this.desc = desc;
	}

	public ResultInfo(String status, String statusDesc, HashMap<String, String> param, String desc) {
		this.status = status;
		this.statusDesc = statusDesc;
		this.param = param;
		this.desc = desc;
	}

	public ResultInfo(String status, String statusDesc, Exception e) {
		this.status = status;
		this.statusDesc = statusDesc;
	}

	public HashMap<String, String> getParam() {
		return param;
	}

	public void setParam(HashMap<String, String> param) {
		this.param = param;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
}
