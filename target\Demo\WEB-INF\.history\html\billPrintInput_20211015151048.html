<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>一体机打印首页</title>
    <link href="css/printstyle.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="../css/sweetalert.css" />
    <link rel="stylesheet" type="text/css" href="../css/virtualkeyboard.css" />
</head>

<body>
    <div class="wrapper">
        <div class="header">
            <div class="logo-ouye"></div>
            <div class="title">加工中心自助打印服务</div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li class="fontblue">1.读取身份证</li>
                    <li class="arrow"></li>
                    <li>2.自助打印</li>
                    <li class="arrow"></li>
                </ul>
            </div>
        </div>
        <div class="container">
            <div class="main">
                <div class="information">
                    <ul>
                        <li><span>身份证</span><input id="id_card" class="ipt1" type="text" placeholder="请输入您的身份证号码" autocomplete="off"></li>
                        <li><span>姓&nbsp;&nbsp;&nbsp;&nbsp;名</span><input id="name" class="ipt1" type="text" placeholder="请输入您的姓名" autocomplete="off"></li>
                        <li><span>车牌号</span><input id="province" class="ipt1" type="text"><input id="license" class="ipt1" type="text"></li>
                    </ul>
                </div>
                <div class="btn">
                    <button id="indexRead" class="read" type="read">读取身份证</button>
                    <button id="query" class="next" type="next">查询车牌号</button>
                    <button id="indexNext" class="next" type="next">下一步</button>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="js/sweetalert-dev.js"></script>
<script type="text/javascript" src="js/virtualkeyboard.js"></script>
<script type="text/javascript" src="js/config.js"></script>
<script>
    var name;
    var id_card;
    var vehicle_id;

    var seg_no = getYtjSegNoConfig();

    //绑定输入法
    $(".ipt1").virtualkeyboard();

    window.onload = function() {
            if ("00126" == seg_no) {
                $(".title").text("佛山宝钢自助打印服务");
            } else if ("00181" == seg_no) {
                $(".title").text("欧冶上海不锈自助打印服务");
            }
        }
        // 身份证读取 - 开启端口
    $("#indexRead").unbind("click").bind("click", function() {
        $.ajax({
            url: "ReadIdCard",
            type: "post",
            async: false,
            success: function(res) {
                //返回状态： 1，身份证读取端口开启成功。
                console.log(JSON.stringify(res));
                if (res.status == "1") {
                    //数据查询
                    getIdentityInfo();
                } else {
                    swal(res.statusDesc, "", "warning");
                    console.log(JSON.stringify(res));
                    return;
                }
            }
        });
    });

    //获取身份证数据
    function getIdentityInfo() {
        $.ajax({
            url: "GetIdCard",
            type: "post",
            async: false,
            success: function(res) {
                console.log(JSON.stringify(res));
                //返回状态： 1，身份证读取成功。
                if (res.status == "1") {
                    localStorage.setItem("id_card", res.param.strID); //身份证号
                    localStorage.setItem("name", res.param.strName); //身份证号
                    //关闭端口
                    swal(res.statusDesc, "", "success");
                    $("#id_card").val(res.param.strID);
                    $("#name").val(res.param.strName);
                    closeComm();
                    queryVehicleId();
                } else {
                    alert(JSON.stringify(res));
                    swal(res.statusDesc, "", "warning");
                    console.log(JSON.stringify(res));
                    return;
                }
            }
        });
    }

    //关闭端口
    function closeComm() {
        $.ajax({
            url: "CloseIdCard",
            type: "post",
            async: false,
            success: function(res) {
                console.log(JSON.stringify(res));
                //返回状态： 1，身份证读取成功。
                if (res.status == "0") {
                    alert(JSON.stringify(res));
                    swal(res.statusDesc, "", "warning");
                    console.log(JSON.stringify(res));
                    return;
                }
            }
        });
    }

    //根据身份证查询车牌号
    function queryVehicleId() {
        name = $("#name").val();
        id_card = $("#id_card").val();
        $.ajax({
            url: ytjServerUrl + "/queryVehicleId",
            type: "post",
            async: false,
            dataType: "jsonp", //指定服务器返回的数据类型
            data: {
                id_card: id_card,
                name: name,
                seg_no: seg_no
            },
            success: function(res) {
                console.log(res.vehicle_id);
                console.log(JSON.stringify(res));
                if (res.desc == "1") {
                    $("#province").val(res.vehicle_id.substring(0, 1));
                    $("#license").val(res.vehicle_id.substring(1));
                }
            }
        });
    }

    //下一步
    $("#indexNext").unbind("click").bind("click", function() {
        id_card = $("#id_card").val();
        name = $("#name").val();
        if (id_card == "" || id_card == null) {
            swal("请输入身份证号！", "", "warning");
            return false;

        }
        if (name == "" || name == null) {
            swal("请输入姓名！", "", "warning");
            return false;

        }
        //车牌框不能为空
        if ($("#province").val() == "" || $("#license").val() == "") {
            swal("请输入车牌号！", "", "warning");
            return false;
        }
        vehicle_id = $("#province").val() + $("#license").val();
        localStorage.setItem("seg_no", seg_no);
        localStorage.setItem("name", name);
        localStorage.setItem("id_card", id_card);
        localStorage.setItem("vehicle_id", vehicle_id);
        window.location.href = "ytjPrintPage";
    });

    //查询车牌号
    $("#query").unbind("click").bind("click", function() {
        queryVehicleId();
    });
</script>

</html>