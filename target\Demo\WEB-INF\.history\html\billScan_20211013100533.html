<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>工贸一体机</title>
    <link rel="stylesheet" type="text/css" href="../css/newstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/sweetalert.css" />
    <link rel="stylesheet" type="text/css" href="../css/virtualkeyboard.css" />

</head>

<body>
    <div class="wrapper">
        <div class="header">
            <div id="logo" class="logo-baosight"></div>
            <div class="title">工贸一体机登记</div>
        </div>
        <div class="nav">
            <div class="navbox">
                <ul>
                    <li>1.读取身份证</li>
                    <li class="arrow"></li>
                    <li class="fontblue">2.选择装卸货</li>
                    <li class="arrow"></li>
                    <li>3.进厂登记</li>
                </ul>
            </div>
        </div>
        <div class="container">
            <div class="main">
                <div class="information-bill">
                    <ul>
                        <li><span>提货业务类型</span></li>
                        <li>
                            <button id="ouye" class="loading" value="10" onclick="selected(this.value)">欧冶上海不锈提货业务</button>
                            <button id="baosteel" class="discharge" value="20" onclick="selected(this.value)">高强钢/宝井提单业务</button>
                            <button id="other" class="discharge" value="30" onclick="selected(this.value)">其他提货业务</button>
                        </li>
                        <li style="margin-top: 30px;"><span class="tag">提单号</span><input id="bill_id" class="ipt1" type="text" placeholder="请扫描提单号"></li>
                    </ul>
                </div>
                <div class="btn">
                    <button class="before" onclick="javascript :history.back(-1);">上一步</button>
                    <button class="next" onclick="nextStep();">下一步</button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="../js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="../js/sweetalert-dev.js"></script>
    <script type="text/javascript" src="../js/virtualkeyboard.js"></script>
    <script type="text/javascript" src="../js/config.js"></script>
    <script>
        var bill_id = localStorage.getItem("bill_id"); //提单号
        var load_business_type = localStorage.getItem("load_business_type"); //提货业务类型
        var seg_no = localStorage.getItem("seg_no");

        //绑定输入法

        $("#bill_id").click(function() {
            $('.virtualkeyboard').show();
        });

        //监听车牌键盘输入
        $('#bill_id').bind('input propertychange', function() {
            var inputContent = $("#bill_id").val();
            console.log("输入内容：" + inputContent);
            $("#bill_id").val(inputContent.toUpperCase());
        });

        //页面加载将车牌号与装卸货填充
        window.onload = function() {
            if (bill_id != null && bill_id != "" &&
                bill_id != "undefined") {
                $("#bill_id").val(bill_id);
            }
            if (load_business_type != null && load_business_type != "" &&
                load_business_type != "undefined") {
                selected(load_business_type);
            }
        }

        $(document).ready(function() {
            //根据segNo显示对应logo
            switchLogo();
        });

        function selected(value, css) {
            if (value == 10) {
                $("#ouye").css("background-image", 'url("images/typeactive.png")');
                $("#baosteel").css("background-image", 'url("images/typebg.png")');
                $("#other").css("background-image", 'url("images/typebg.png")');
            } else if (value == 20) {
                $("#ouye").css("background-image", 'url("images/typebg.png")');
                $("#baosteel").css("background-image", 'url("images/typeactive.png")');
                $("#other").css("background-image", 'url("images/typebg.png")');
            } else {
                $("#ouye").css("background-image", 'url("images/typebg.png")');
                $("#baosteel").css("background-image", 'url("images/typebg.png")');
                $("#other").css("background-image", 'url("images/typeactive.png")');
            }

            //选择装卸货时，将装卸货类型保存
            hand_big_type = value;
            localStorage.setItem("load_business_type", value);
        }

        //下一步
        function nextStep() {
            //车牌框不能为空
            if ($("#bill_id").val() == "" || $("#bill_id").val() == "") {
                swal("请输扫描提单号！", "", "warning");
                return false;
            }
            var licenseVal = $("#bill_id").val();
            console.log("当前segNo=" + seg_no);
            if (load_business_type == null || load_business_type == "" ||
                load_business_type == "undefined") {
                swal("请选择提货业务类型！", "", "warning");
                return false;
            }
            //以防手动修改车牌号，在跳转下个页面时保存车牌号
            localStorage.setItem("vehicle_id", $("#province").val() +
                $("#license").val());
            window.location.href = "nextStep";
        }
    </script>
</body>

</html>