<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>加工中心自助打印服务</title>
    <link rel="stylesheet" type="text/css" href="../css/printstyle.css">
    <link rel="stylesheet" type="text/css" href="../css/sweetalert.css" />
    <link rel="stylesheet" type="text/css" href="../css/virtualkeyboard.css" />
    <style type="text/css">
        .sp-grid-import {
            border-collapse: collapse;
            width: 100%;
            border: 1px solid #E1E6EB;
            border-left: none;
            table-layout: fixed;
        }
        
        .sp-grid-import thead th {
            line-height: 20px;
            padding: 8px 12px;
            border-bottom: 1px solid #E1E6EB;
            border-left: 1px solid #E1E6EB;
            white-space: nowrap;
            text-align: center;
            font-weight: normal !important;
            letter-spacing: 1px;
        }
        
        .sp-grid-import tbody td {
            text-align: center;
            line-height: 20px;
            padding: 8px 10px;
            font-size: 13px;
            border-bottom: 1px solid #E1E6EB;
            border-left: 1px solid #E1E6EB;
        }
    </style>
</head>

<body>

    <body>
        <div class="wrapper">
            <div class="header">
                <div class="logo-ouye"></div>
                <div class="title">加工中心自助打印服务</div>
            </div>
            <div class="line"></div>
            <div class="content">
                <div class="box" style="overflow-x: auto; overflow-y: auto;">
                    <table class="sp-grid-import" id="table-bill" border="1" width="100%">
                        <tr>
                            <th>勾选</th>
                            <th>客户</th>
                            <th>提单号</th>
                            <th>发货仓库</th>
                            <th>提单重量</th>
                            <th>打印次数</th>
                            <th>提单类型</th>
                        </tr>
                        <!-- <tr id="tr"></tr>
                        <tr class="bill-data">
                            <td><input type="checkbox"></td>
                            <td>客户A</td>
                            <td>EL21212121212</td>
                            <td>欧冶不锈上海</td>
                            <td>10</td>
                            <td>0</td>
                            <td>发货通知书</td>
                        </tr>
                        <tr class="bill-data">
                            <td><input type="checkbox"></td>
                            <td>客户B</td>
                            <td>CA11223344</td>
                            <td>欧冶不锈上海</td>
                            <td>10</td>
                            <td>0</td>
                            <td>出库申请单</td>
                        </tr> -->
                    </table>
                </div>
            </div>
            <div class="footer">
                <div class="par"></div>
                <div>
                    <button class="bill_print_exit">退出</button>
                    <button class="bill_print">打印</button>
                </div>
            </div>
    </body>
    <script type="text/javascript" src="../js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="../js/sweetalert-dev.js"></script>
    <script type="text/javascript" src="../js/virtualkeyboard.js"></script>
    <script type="text/javascript" src="../js/shadow_shade.js"></script>
    <script>
        var seg_no = localStorage.getItem("seg_no");
        var vehicle_id = localStorage.getItem("vehicle_id");
        var id_card = localStorage.getItem("id_card");

        //打印获取配车单号
        var print_allocate_vehicle_id;

        window.onload = function() {
            if ("00126" == seg_no) {
                $(".title").text("佛山宝钢自助打印服务");
            } else if ("00181" == seg_no) {
                $(".title").text("欧冶上海不锈自助打印服务");
            }
            // queryAllocateInfo();
            //testy();
        }

        function queryAllocateInfo() {
            $.ajax({
                url: ytjServerUrl + "queryAllocateInfo",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    seg_no: seg_no,
                    vehicle_id: vehicle_id,
                    id_card: id_card
                },
                success: function(res) {
                    console.log(res);
                    console.log(JSON.stringify(res));
                    var ListInfo = res.resultMap;
                    var tr = '<tr>';

                    tr += '</tr>'

                    $("#table-bill").html(td);
                }

            });
        }

        //退出  返回首页
        $(".bill_print_exit").unbind("click").bind("click", function() {
            window.location.href = "ytjPrintIndex";
        });

        //打印
        $(".bill_print").unbind("click").bind("click", function() {
            var trList = $("#table-bill tr");
            var billArray = new Array();
            for (var i = 2; i < trList.length; i++) {
                var tdArr = trList.eq(i).find("td");
                var checkBox = tdArr.eq(0).find('input[type="checkbox"]');
                var bill_id = tdArr.eq(2).text();
                if ($(checkBox).is(':checked')) {
                    alert("第" + (i - 2) + "个选中了:" + bill_id);
                }
            }

        });

        function doPrint(vehicle_id, car_trace_no, allocate_vehicle_id) {
            shadow_shade.show();
            //alert(vehicle_id + car_trace_no);
            $.ajax({
                url: ytjServerUrl + "queryPrintUrl",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    seg_no: seg_no,
                    vehicle_id: vehicle_id,
                    car_trace_no: car_trace_no
                },
                success: function(res) {
                    console.log(res);
                    console.log(JSON.stringify(res));
                    if (res.desc == "1") {
                        //跳转报表地址
                        //window.location.href = res.resultMap;
                        //本地处理打印服务
                        print_allocate_vehicle_id = allocate_vehicle_id;
                        doPrintLocal(res.resultMap);
                        //本地预览报表
                        //window.open("/file/printDoc.pdf");
                    }
                }
            });
        }

        //本地连接打印机，打印pdf
        function doPrintLocal(addrLink) {
            $.ajax({
                url: "doPrintLocal",
                type: "post",
                async: false,
                data: {
                    addrLink: addrLink
                },
                success: function(res) {
                    shadow_shade.hidden();
                    //alert(res);
                    if ("1" == res) {
                        swal("打印成功", "", "success");
                        //打印成功后配车单上增加打印标记
                        addFlagforPrint();
                        //window.open("/file/printDoc.pdf",'MySmart', 'top=100,left=180,toolbar=no,status=no,location=no,resizable=no,menubar=no,scrollbars=no,resizable=no,width=900,height=600');
                    } else {
                        swal("打印失败,请重新打印", "", "warning");
                    }
                }
            });
        }

        //打印成功后配车单上增加打印标记
        function addFlagforPrint() {
            //alert(vehicle_id + car_trace_no);
            $.ajax({
                url: ytjServerUrl + "addFlagforPrint",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    seg_no: seg_no,
                    allocate_vehicle_id: print_allocate_vehicle_id
                },
                success: function(res) {

                }
            });
        }
    </script>

</html>