@charset "UTF-8";

/* CSS Document */

html,
body {
    height: 100%;
}

body {
    width: 100%;
    margin: 0;
    padding: 0;
    color: #333;
    font-size: 14px;
    font-family: Microsoft YaHei, sans-serif;
    background-color: #f4f4f4;
    background-image: url("../images/bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100%;
    overflow-y: hidden
}

ul,
li {
    list-style: none;
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
}

p {
    padding: 0;
    margin: 0;
}


/**********************************************page1 begin*/

.wrapper {
    width: 100%;
    height: 100%;
}

.header {
    width: 100%;
    height: 100px;
    background: #e5ebf3;
}

.logo {
    width: 212px;
    height: 45px;
    background: url(../images/logo-ouye.png) no-repeat;
    margin: 25px 0 0 50px;
    float: left;
}

.title {
    font-size: 30px;
    height: 100px;
    line-height: 100px;
    margin: 0 50px 0 0;
    float: right
}

.nav {
    width: 100%;
    height: 80px;
    line-height: 80px;
    background: #203890;
    color: #fff;
    float: left;
}

.navbox {
    width: 1150px;
    margin: 0 auto;
}

.navbox li {
    width: 33.3%;
    height: 80px;
    float: left;
    display: block;
    font-size: 22px;
    text-align: center;
}

.fontblue {
    color: #aff;
}

.container {
    width: 1080px;
    margin: 140px auto;
    background-image: url("../images/bg.png");
}

.arrow {
    background: url(../images/arrow.png) no-repeat 50% 50%;
}

.main {
    width: 1080px;
    height: 250px;
    margin-top: 30px;
}

.information {
    float: left;
    height: 250px;
    background-image: url("../images/informationbg.jpg");
    background-repeat: no-repeat;
    padding: 25px;
}

.information ul li {
    margin-bottom: 20px;
    margin-left: 10px;
}

.information span {
    float: left;
    width: 72px;
    height: 50px;
    line-height: 50px;
    display: block;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
}

#id_card {
    width: 430px;
    height: 50px;
    background-color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

#name {
    width: 430px;
    height: 50px;
    background-color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

#province {
    width: 80px;
    height: 50px;
    background-color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
}

#license {
    width: 338px;
    height: 50px;
    background-color: #ffffff;
    border: 1px solid #666666;
    border-radius: 4px;
    text-indent: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    font-family: Microsoft Yahei, sans-serif;
    outline-style: none;
    margin-left: 5px;
}

.btn {
    width: 220px;
    height: 180px;
    float: right;
}

.btn button {
    width: 220px;
    height: 80px;
    background-image: url("../images/btnbg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    border: none;
    outline-style: none;
    font-size: 20px;
    color: #fff;
    text-indent: 10px;
    font-family: Microsoft Yahei, sans-serif;
    cursor: pointer；
}

.btn button:active {
    background-image: url("../images/btnhover.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.next {
    margin-top: 20px;
}


/**********************************************page1 end*/


/**********************************************page2 begin*/

.line {
    height: 4px;
    background-color: #203890;
}

.content {
    width: 100%;
    height: 75%;
    margin: 20px auto;
}

.box {
    width: 98%;
    height: 100%;
    background: #ffffff;
    border: solid 1px #B5E3EF;
    margin: 0 auto;
}

.line {
    width: 100%;
    height: 4px;
    background: #203890;
}

.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 60px;
    background-color: #E5EBF3;
}

.par {
    width: 100%;
    height: 1px;
    background-color: #dddddd;
}

.exit {
    float: right;
    width: 80px;
    height: 40px;
    background-color: #1C93BF;
    border-radius: 4px;
    font-size: 20px;
    color: #ffffff;
    outline-style: none;
    margin: 10px 40px;
}


/**********************************************page2 end*/

#printBtn {
    width: 180px;
    height: 30px;
    background-color: #1C93BF;
    border-radius: 4px;
    font-size: 20px;
    color: #ffffff;
    outline-style: none;
}

#printTd {
    height: 40px;
    width: 200px;
}

#Printed {
    color: red;
}

#putoutTd {
    height: 40px;
    width: 20px;
}