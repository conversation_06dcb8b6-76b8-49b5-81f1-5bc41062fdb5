<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<bean id="configProperties"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:jdbc.properties</value>
			</list>
		</property>
	</bean>

	<!-- 配置数据源 -->
	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass" value="${jdbc.driverClassName}" />
		<property name="jdbcUrl" value="${jdbc.url}" />
		<property name="user" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<property name="minPoolSize" value="${c3p0.minPoolSize}" />
		<property name="acquireIncrement" value="${c3p0.acquireIncrement}" />
		<property name="acquireRetryAttempts" value="${c3p0.acquireRetryAttempts}" />
		<property name="acquireRetryDelay" value="${c3p0.acquireRetryDelay}" />
		<property name="autoCommitOnClose" value="${c3p0.autoCommitOnClose}" />
		<property name="checkoutTimeout" value="${c3p0.checkoutTimeout}" />
		<property name="idleConnectionTestPeriod" value="${c3p0.idleConnectionTestPeriod}" />
		<property name="maxIdleTime" value="${c3p0.maxIdleTime}" />
		<property name="testConnectionOnCheckin" value="${c3p0.testConnectionOnCheckin}" />
		<property name="maxStatements" value="${c3p0.maxStatements}" />
		<property name="maxStatementsPerConnection" value="${c3p0.maxStatementsPerConnection}" />
		<property name="unreturnedConnectionTimeout" value="${c3p0.unreturnedConnectionTimeout}" />
	</bean>

	<!-- 配置mybatisSqlSessionFactoryBean -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="mapperLocations" value="classpath:Dao/*.xml" />
	</bean>
	<!-- 配置mybatis mapper接口 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="Dao" />
		<!--<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/> -->
	</bean>

	<!-- 配置事务管理器 -->
	<bean id="transactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>

</beans>