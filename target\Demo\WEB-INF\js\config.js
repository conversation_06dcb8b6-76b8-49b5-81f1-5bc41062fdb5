//TODO 一体机区域公司配置code,规格：segNo+厂区序号，如0012901是00129天津宝钢一厂
var ytjSegNoConfig = '0012601';
//一体机服务端地址
//测试机
var ytjServerUrl = "http://***********:8080/BaosightYTJApplication/";
//本地
// var ytjServerUrl = "http://localhost:8085/SSM_BaosightYTJApplication_war/";
//正式机
// var ytjServerUrl = "http://***********/Demo-1.0-SNAPSHOT/";
// var ytjServerUrl = "http://ksh.baointl.com/Demo-1.0-SNAPSHOT/";

function getYtjSegNoConfig() {
    return ytjSegNoConfig.substring(0, 5);
}

function getYtjFactoryConfig() {
    console.log(123)
    var segNo = getYtjSegNoConfig();
    var factoryCode = ytjSegNoConfig.substring(5);
    var factoryName = '';
    switch (segNo) {
        case '00118':
            //花都
            if (factoryCode == '01') {
                factoryName = '一厂';
            } else if (factoryCode == '02') {
                factoryName = '二厂';
            }
            break;
        case '00126':
            //佛宝
            factoryName = '一厂';
            break;
        case '00129':
            //天津宝钢
            factoryName = '天津宝钢';
            break;
        case '00145':
            //天津宝井
            factoryName = '天津宝井';
            break;
        case '00110':
        case '00181':
            //上海不秀
            factoryName = '欧冶上海不锈';
            break;
    }
    console.log("getYtjFactoryConfig：segNo->" + segNo + "  factoryCode->" + factoryCode + " factoryName->" + factoryName);
    return factoryName;
}

function switchLogo() {
    var segNo = getYtjSegNoConfig();
    if (segNo == '00126' || segNo == "00181") {
        $('#logo').removeClass('logo-baosight');
        $('#logo').addClass('logo-ouye');
    }
}