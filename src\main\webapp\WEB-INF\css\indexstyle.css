@charset "UTF-8";

/* CSS Document */

html {
    height: 100%;
}

body {
    background-color: #e5ebf3;
    width: 98%;
    height: 100%;
    margin: 0;
    margin-top: -1%;
    padding: 0 1%;
}

.top {
    width: 98%;
    height: 120px;
    position: absolute;
    top: 0;
    background: url(../images/linebg.png) repeat-x;
    border-right: 1px solid #e5ebf3;
}

.logo-baosight {
    width: 272px;
    height: 60px;
    background: url(../images/logo-baosight.png) no-repeat;
    margin: 30px 0 0 0px;
    float: left;
}

.logo-ouye {
    width: 272px;
    height: 60px;
    background: url(../images/logo-ouye.png) no-repeat;
    margin: 15px 0 0 0px;
    float: left;
}

.content {
    width: 100%;
    height: 100%;
}

.entrance {
    width: 100%;
    height: 100%;
    background-image: url("../images/left.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 0 120px;
}

.sel {
    float: right;
    width: 40%;
    height: 100%;
    background-color: #203890;
}

.cho {
    position: absolute;
    width: 238px;
    left: 72%;
    top: 45%;
}

.cho button {
    width: 238px;
    height: 80px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.2);
    outline-style: none;
    margin-bottom: 16%;
    color: #ffffff;
    font-size: 20px;
}