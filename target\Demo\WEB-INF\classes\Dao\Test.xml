<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Dao.Test">

	<resultMap id="UserResultMap" type="Entity.User">
		<id column="firstName" jdbcType="VARCHAR" property="firstName" />
	</resultMap>
	<select id="selectByPrimaryKey" resultType="java.lang.String">
		SELECT '1' as "firstName" FROM dual
	</select>

</mapper>