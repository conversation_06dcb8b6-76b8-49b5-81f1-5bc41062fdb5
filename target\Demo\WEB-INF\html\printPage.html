<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>加工中心自助打印服务</title>
    <link href="css/printstyle.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="css/sweetalert.css" />
    <link rel="stylesheet" type="text/css" href="css/virtualkeyboard.css" />
    <style type="text/css">
        .sp-grid-import {
            border-collapse: collapse;
            width: 100%;
            border: 1px solid #E1E6EB;
            border-left: none;
            table-layout: fixed;
        }
        
        .sp-grid-import thead th {
            line-height: 20px;
            padding: 8px 12px;
            border-bottom: 1px solid #E1E6EB;
            border-left: 1px solid #E1E6EB;
            white-space: nowrap;
            text-align: center;
            font-weight: normal !important;
            letter-spacing: 1px;
        }
        
        .sp-grid-import tbody td {
            text-align: center;
            line-height: 20px;
            padding: 8px 10px;
            font-size: 13px;
            border-bottom: 1px solid #E1E6EB;
            border-left: 1px solid #E1E6EB;
        }

        /* 签名模态窗口样式 */
        .signature-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .signature-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            position: relative;
        }

        .signature-header {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .signature-canvas-container {
            border: 2px solid #ccc;
            border-radius: 5px;
            background-color: #fff;
            margin: 20px 0;
            text-align: center;
        }

        #signatureCanvas {
            border: none;
            cursor: crosshair;
        }

        .signature-buttons {
            text-align: center;
            margin-top: 20px;
        }

        .signature-btn {
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .signature-btn.confirm {
            background-color: #28a745;
            color: white;
        }

        .signature-btn.confirm:hover {
            background-color: #218838;
        }

        .signature-btn.clear {
            background-color: #ffc107;
            color: white;
        }

        .signature-btn.clear:hover {
            background-color: #e0a800;
        }

        .signature-btn.cancel {
            background-color: #dc3545;
            color: white;
        }

        .signature-btn.cancel:hover {
            background-color: #c82333;
        }

        .signature-btn:disabled {
            background-color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        .signature-btn:disabled:hover {
            background-color: #6c757d !important;
        }

        .signature-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .signature-info h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .signature-info p {
            margin: 5px 0;
            color: #6c757d;
        }
    </style>
</head>

<body>

    <body>
        <div class="wrapper">
            <div class="header">
                <div class="logo-ouye"></div>
                <div class="title">加工中心自助打印服务</div>
                <div id="countdown" class="title1"></div>
            </div>
            <div class="line"></div>
            <div class="content">
                <div class="box" style="overflow-x: auto; overflow-y: auto;">
                    <table class="sp-grid-import" id="table-7" border="1" width="100%">
                        <tr>
                            <th>账套号</th>
                            <th>车牌号</th>
                            <th>提单重量</th>
                            <th>客户名称</th>
                            <th>出库时间</th>
                            <th>操作</th>
                        </tr>
                        <!-- <tr id="tr"></tr>
                        <tr>
                            <td>00181</td>
                            <td>苏A12368</td>
                            <td>10</td>
                            <td>测试</td>
                            <td>2021.10.18 18：00</td>
                            <td id="printTd"><button id="printBtn">打印</button></td>
                        </tr> -->
                    </table>
                </div>
            </div>
            <div class="footer">
                <div class="par"></div>
                <div>
                    <p style="color: red; float: left; outline-style: none; margin: 10px 40px; font-size: 19px; padding-left: 70px;">
                        *出库单据打印视同清单货物接收确认</p>
                    <button class="exit">退出</button>
                </div>
            </div>
        </div>

        <!-- 签名模态窗口 -->
        <div id="signatureModal" class="signature-modal">
            <div class="signature-content">
                <div class="signature-header">电子签名确认</div>

                <div class="signature-info">
                    <h4>签名信息：</h4>
                    <p><strong>账套号：</strong><span id="signInfoSegNo"></span></p>
                    <p><strong>车牌号：</strong><span id="signInfoVehicleNo"></span></p>
                    <p><strong>总重量：</strong><span id="signInfoWeight"></span></p>
                    <p><strong>客户名称：</strong><span id="signInfoCustName"></span></p>
                    <p style="color: #28a745; font-size: 14px; margin-top: 10px;"><strong>注意：此次签名将应用于所有需要签名的记录</strong></p>
                </div>

                <div class="signature-canvas-container">
                    <p style="margin: 10px 0; color: #666;">请在下方区域签名：</p>
                    <canvas id="signatureCanvas" width="700" height="300"></canvas>
                </div>

                <div class="signature-buttons">
                    <button class="signature-btn confirm" onclick="confirmSignature()">确认签名</button>
                    <button class="signature-btn clear" onclick="clearSignature()">清除重签</button>
                    <button class="signature-btn cancel" onclick="cancelSignature()">取消</button>
                </div>
            </div>
        </div>

        <!-- 隐藏的iframe和form用于提交签名数据 -->
        <iframe id="signatureSubmitFrame" name="signatureSubmitFrame" style="display: none;"></iframe>
        <form id="signatureSubmitForm" target="signatureSubmitFrame" method="POST" style="display: none;">
            <input type="hidden" name="seg_no" id="formSegNo">
            <input type="hidden" name="signature_base64" id="formSignatureBase64">
            <input type="hidden" name="signature_list" id="formSignatureList">
        </form>

    </body>
    <script type="text/javascript" src="js/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="js/sweetalert-dev.js"></script>
    <script type="text/javascript" src="js/virtualkeyboard.js"></script>
    <script type="text/javascript" src="js/shadow_shade.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script>
        var seg_no = localStorage.getItem("seg_no");
        var vehicle_id = localStorage.getItem("vehicle_id");
        var id_card = localStorage.getItem("id_card");
        var countdownS = 179;

        //打印获取配车单号
        var print_allocate_vehicle_id;

        // 签名相关变量
        var canvas, ctx;
        var isDrawing = false;
        var currentSignatureData = null; // 当前签名的数据信息
        var pendingSignatureList = []; // 待签名数据列表
        var currentSignatureIndex = 0; // 当前签名数据索引

        $(document).ready(function() {
            // setTimeout(function() { window.history.back() }, 180000);
            // setInterval(() => {
            //     $("#countdown").html(`页面回退倒计时: <span style="color:red;">${countdownS}</span> 秒`);
            //     countdownS--;
            // }, 1000);
            
            // 初始化签名画布
            initSignatureCanvas();
        });

        window.onload = function() {
            if ("00126" == seg_no) {
                $(".title").text("佛山宝钢自助打印服务");
            } else if ("00181" == seg_no) {
                $(".title").text("欧冶上海不锈自助打印服务");
            }
            queryAllocateInfo();
            //testy();
        }

        // 初始化签名画布
        function initSignatureCanvas() {
            canvas = document.getElementById('signatureCanvas');
            ctx = canvas.getContext('2d');
            
            // 设置画笔样式
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // 鼠标事件
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);

            // 触摸事件（支持触屏设备）
            canvas.addEventListener('touchstart', function(e) {
                e.preventDefault();
                var touch = e.touches[0];
                var rect = canvas.getBoundingClientRect();
                var x = touch.clientX - rect.left;
                var y = touch.clientY - rect.top;
                ctx.beginPath();
                ctx.moveTo(x, y);
                isDrawing = true;
            });

            canvas.addEventListener('touchmove', function(e) {
                e.preventDefault();
                if (!isDrawing) return;
                var touch = e.touches[0];
                var rect = canvas.getBoundingClientRect();
                var x = touch.clientX - rect.left;
                var y = touch.clientY - rect.top;
                ctx.lineTo(x, y);
                ctx.stroke();
            });

            canvas.addEventListener('touchend', function(e) {
                e.preventDefault();
                isDrawing = false;
            });
        }

        function startDrawing(e) {
            isDrawing = true;
            var rect = canvas.getBoundingClientRect();
            ctx.beginPath();
            ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
        }

        function draw(e) {
            if (!isDrawing) return;
            var rect = canvas.getBoundingClientRect();
            ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
            ctx.stroke();
        }

        function stopDrawing() {
            isDrawing = false;
        }

        // 清除签名
        function clearSignature() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 显示批量签名模态窗口
        function showBatchSignatureModal() {
            // 设置当前签名数据为第一条，用于显示信息
            currentSignatureData = pendingSignatureList[0];
            
            // 填充签名信息
            document.getElementById('signInfoSegNo').textContent = '佛山宝钢';
            
            // 如果只有一条数据，显示具体信息
            if (pendingSignatureList.length === 1) {
                document.getElementById('signInfoVehicleNo').textContent = pendingSignatureList[0].vehicle_no;
                document.getElementById('signInfoWeight').textContent = pendingSignatureList[0].total_weight + '吨';
                document.getElementById('signInfoCustName').textContent = pendingSignatureList[0].cust_name;
                document.querySelector('.signature-header').textContent = '电子签名确认';
            } else {
                // 多条数据，显示汇总信息
                var vehicleNos = pendingSignatureList.map(item => item.vehicle_no).join('、');
                var totalWeight = pendingSignatureList.reduce((sum, item) => sum + parseFloat(item.total_weight), 0);
                var custNames = [...new Set(pendingSignatureList.map(item => item.cust_name))].join('、');
                
                document.getElementById('signInfoVehicleNo').textContent = vehicleNos;
                document.getElementById('signInfoWeight').textContent = totalWeight.toFixed(2) + '吨';
                document.getElementById('signInfoCustName').textContent = custNames;
                document.querySelector('.signature-header').textContent = `批量签名确认 (共${pendingSignatureList.length}条记录)`;
            }
            
            // 清除之前的签名
            clearSignature();
            
            // 显示模态窗口
            document.getElementById('signatureModal').style.display = 'block';
        }

        // 确认签名
        function confirmSignature() {
            // 检查是否有签名内容
            var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            var hasSignature = false;
            
            for (var i = 0; i < imageData.data.length; i += 4) {
                if (imageData.data[i + 3] > 0) {
                    hasSignature = true;
                    break;
                }
            }
            
            if (!hasSignature) {
                swal("请先签名", "", "warning");
                return;
            }

            // 获取签名的base64数据
            var signatureBase64 = canvas.toDataURL('image/png');

            // 移除data:image/png;base64,前缀
            signatureBase64 = signatureBase64.replace(/^data:image\/png;base64,/, '');

            // 发送签名数据到后台
            submitSignature(signatureBase64, currentSignatureData);
        }

        // 取消签名
        function cancelSignature() {
            swal({
                title: "取消签名",
                text: pendingSignatureList.length > 1 ? 
                      `确定要取消签名吗？这将跳过${pendingSignatureList.length}条记录的签名。` : 
                      "确定要取消签名吗？",
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "确定取消",
                cancelButtonText: "继续签名"
            }, function(isConfirm) {
                if (isConfirm) {
                    // 取消签名
                    document.getElementById('signatureModal').style.display = 'none';
                    currentSignatureData = null;
                    pendingSignatureList = [];
                    currentSignatureIndex = 0;
                }
            });
        }

        // 提交批量签名数据
        function submitSignature(signatureBase64, data) {
            shadow_shade.show();
            
            // 准备批量提交的数据
            var batchData = {
                seg_no: seg_no,
                signature_base64: signatureBase64,
                signature_list: []
            };
            
            // 将所有待签名的数据添加到列表中
            for (var i = 0; i < pendingSignatureList.length; i++) {
                batchData.signature_list.push({
                    allocate_vehicle_id: pendingSignatureList[i].allocate_vehicle_id,
                    vehicle_no: pendingSignatureList[i].vehicle_no,
                    car_trace_no: pendingSignatureList[i].car_trace_no,
                    cust_name: pendingSignatureList[i].cust_name,
                    total_weight: pendingSignatureList[i].total_weight
                });
            }
            // 使用隐藏的iframe+form提交数据，避免CORS和URL长度限制
            var form = document.getElementById('signatureSubmitForm');
            var iframe = document.getElementById('signatureSubmitFrame');

            // 设置form的action
            form.action = ytjServerUrl + "saveSign";

            // 填充表单数据
            document.getElementById('formSegNo').value = batchData.seg_no;
            document.getElementById('formSignatureBase64').value = batchData.signature_base64;
            document.getElementById('formSignatureList').value = JSON.stringify(batchData.signature_list);

            // 接口返回状态跟踪
            var apiResponse = null; // 用来接收接口返回的数据
            var responseReceived = false; // 标记是否收到了接口响应

            // 尝试设置iframe的onload回调
            iframe.onload = function() {
                // 避免页面初始加载时触发
                if (!iframe.src || iframe.src === 'about:blank') {
                    return;
                }

                console.log("iframe onload 触发");
                responseReceived = true;

                try {
                    // 尝试读取iframe中的响应
                    var response = iframe.contentDocument || iframe.contentWindow.document;
                    var responseText = response.body.textContent || response.body.innerText;

                    console.log("服务器响应:", responseText);
                    apiResponse = responseText; // 保存接口返回的数据

                    // 立即处理响应
                    handleApiResponse(responseText);

                } catch (e) {
                    console.log("无法读取iframe响应（跨域限制）:", e);
                    // 跨域限制时，标记已收到响应但无法读取内容
                    apiResponse = "跨域限制，无法读取响应内容";
                    handleApiResponse(null);
                }
            };

            // 处理API响应的统一函数
            function handleApiResponse(responseText) {
                shadow_shade.hidden();

                var isSuccess = false;
                var errorMessage = "";

                if (responseText && responseText.trim()) {
                    // 检查是否包含错误信息
                    if (responseText.includes("错误") || responseText.includes("失败") || responseText.includes("异常")) {
                        errorMessage = responseText;
                    } else {
                        // 先尝试解析为JSON
                        try {
                            var res = JSON.parse(responseText);
                            isSuccess = (res.desc == "1");
                            if (!isSuccess) {
                                errorMessage = "服务器返回失败状态";
                            }
                        } catch (parseError) {
                            // 如果不是JSON格式，检查是否包含成功信息
                            isSuccess = responseText.includes("操作成功") || responseText.includes("成功");
                            if (!isSuccess) {
                                errorMessage = responseText;
                            }
                        }
                    }
                } else {
                    // 跨域限制时假设成功
                    isSuccess = true;
                }

                if (isSuccess) {
                    if (pendingSignatureList.length === 1) {
                        swal("签名提交成功", "", "success").then(function() {
                            document.getElementById('signatureModal').style.display = 'none';
                        });
                    } else {
                        swal("批量签名完成", `已为${pendingSignatureList.length}条记录完成签名`, "success").then(function() {
                            document.getElementById('signatureModal').style.display = 'none';
                        });
                    }

                    // 清空待签名列表
                    pendingSignatureList = [];
                    currentSignatureIndex = 0;

                    // 刷新数据显示最新状态
                    queryAllocateInfo();
                } else {
                    swal("签名提交失败：" + errorMessage, "", "error").then(function() {
                        document.getElementById('signatureModal').style.display = 'none';
                    });
                }
            }

            // 设置超时检查
            setTimeout(function() {
                if (!responseReceived) {
                    // 真正的超时：接口没有返回任何数据
                    shadow_shade.hidden();
                    swal("请求超时，服务器无响应，请检查网络连接后重试", "", "error").then(function() {
                        document.getElementById('signatureModal').style.display = 'none';
                    });
                }
            }, 30000); // 10秒超时

            // 提交表单
            form.submit();
        }

        function queryAllocateInfo() {
            $.ajax({
                url: ytjServerUrl + "queryAllocateInfo",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    seg_no: seg_no,
                    vehicle_id: vehicle_id,
                    id_card: id_card
                },
                success: function(res) {
                    console.log(res);
                    console.log(JSON.stringify(res));
                    var ListInfo = res.resultMap;
                    var td = '<tr><th>账套号</th><th>车牌号</th><th>提单重量</th><th>客户名称</th><th>出库时间</th><th>操作</th></tr>';
                    
                    // 重置待签名列表
                    pendingSignatureList = [];
                    
                    for (var key in ListInfo) {
                        var seg_no_name = ListInfo[key].seg_no;
                        if ("00126" == ListInfo[key].seg_no) {
                            seg_no_name = "佛山宝钢";
                        } else if ("00181" == ListInfo[key].seg_no) {
                            seg_no_name = "欧冶上海不锈";
                        }

                        // 为佛山宝钢添加签名功能
                        var actionButtons = '';
                        // ("00126" == seg_no && ListInfo[key].need_signature == "1" && ListInfo[key].signature_status != "1");
                        // 判断是否需要签名且还未签名
                        var needSignatureButNotSigned = true;
                        
                        // 如果需要签名但还未签名，加入待签名列表
                        if (needSignatureButNotSigned) {
                            pendingSignatureList.push({
                                seg_no_name: seg_no_name,
                                vehicle_no: ListInfo[key].vehicle_no,
                                car_trace_no: ListInfo[key].car_trace_no,
                                allocate_vehicle_id: ListInfo[key].allocate_vehicle_id,
                                cust_name: ListInfo[key].cust_name,
                                total_weight: ListInfo[key].total_weight
                            });
                        }
                        
                        // 打印按钮 - 如果需要签名但还未签名，则禁用打印按钮
                        if (needSignatureButNotSigned) {
                            actionButtons += '<button class="signature-btn" disabled title="请先完成签名确认">打印(需签名)</button>';
                        } else {
                            actionButtons += '<button class="signature-btn" onclick="doPrint(' +
                                "'" + ListInfo[key].vehicle_no + "','" + 
                                ListInfo[key].car_trace_no + "','" +
                                ListInfo[key].allocate_vehicle_id + "'" + 
                                ')">打印</button>';
                        }
                        
                        // 佛山宝钢签名状态显示
                        if (needSignatureButNotSigned) {
                            actionButtons += '<br><span style="color: orange; font-size: 12px;">待签名</span>';
                        }    else if ("00126" == seg_no && ListInfo[key].signature_status == "1") {
                            actionButtons += '<br><span style="color: green; font-size: 12px;">已签名</span>';
                        }

                        if ("1" == ListInfo[key].ytj_print_flag) {
                            td += '<tr id="Printed"><td>' +
                                seg_no_name +
                                '</td><td>' +
                                ListInfo[key].vehicle_no +
                                '</td><td>' +
                                ListInfo[key].total_weight +
                                '吨' +
                                '</td><td>' +
                                ListInfo[key].cust_name +
                                '</td><td id="putoutTd">' +
                                ListInfo[key].putout_date +
                                '</td><td id="printTd">' + actionButtons + '</td></tr>';
                        } else {
                            td += '<tr><td>' +
                                seg_no_name +
                                '</td><td>' +
                                ListInfo[key].vehicle_no +
                                '</td><td>' +
                                ListInfo[key].total_weight +
                                '吨' +
                                '</td><td>' +
                                ListInfo[key].cust_name +
                                '</td><td id="putoutTd">' +
                                ListInfo[key].putout_date +
                                '</td><td id="printTd">' + actionButtons + '</td></tr>';
                        }
                    }
                    $("#table-7").html(td);
                    
                    // 检查是否有待签名的数据，如果有则自动弹出签名窗口
                    if (pendingSignatureList.length > 0) {
                        showBatchSignatureModal();
                    }
                }

            });
        }



        //退出  返回首页
        $(".exit").unbind("click").bind("click", function() {
            if (seg_no == '00181') {
                window.location.href = "ytjindex";
            } else {
                window.location.href = "ytjPrintIndex";
            }
        });

        function doPrint(vehicle_id, car_trace_no, allocate_vehicle_id) {
            shadow_shade.show();
            //alert(vehicle_id + car_trace_no);
            $.ajax({
                url: ytjServerUrl + "queryPrintUrl",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    seg_no: seg_no,
                    vehicle_id: vehicle_id,
                    car_trace_no: car_trace_no
                },
                success: function(res) {
                    console.log(res);
                    console.log(JSON.stringify(res));
                    if (res.desc == "1") {
                        //跳转报表地址
                        //window.location.href = res.resultMap;
                        //本地处理打印服务
                        print_allocate_vehicle_id = allocate_vehicle_id;
                        doPrintLocal(res.resultMap);
                        //本地预览报表
                        //window.open("/file/printDoc.pdf");
                    } else {
                        shadow_shade.hidden();
                        swal("查询打印地址失败，请重试", "", "error");
                    }
                },
                error: function() {
                    shadow_shade.hidden();
                    swal("网络请求失败，请重试", "", "error");
                }
            });
        }

        //本地连接打印机，打印pdf
        function doPrintLocal(addrLink) {
            $.ajax({
                url: "doPrintLocal",
                type: "post",
                async: false,
                data: {
                    seg_no: seg_no,
                    addrLink: addrLink
                },
                success: function(res) {
                    shadow_shade.hidden();
                    //alert(res);
                    if ("1" == res) {
                        swal("打印成功", "", "success");
                        //打印成功后配车单上增加打印标记
                        addFlagforPrint();
                        //window.open("/file/printDoc.pdf",'MySmart', 'top=100,left=180,toolbar=no,status=no,location=no,resizable=no,menubar=no,scrollbars=no,resizable=no,width=900,height=600');
                    } else {
                        swal("打印失败,请重新打印", "", "warning");
                    }
                },
                error: function() {
                    shadow_shade.hidden();
                    swal("打印服务连接失败，请重试", "", "error");
                }
            });
        }

        //打印成功后配车单上增加打印标记
        function addFlagforPrint() {
            //alert(vehicle_id + car_trace_no);
            $.ajax({
                url: ytjServerUrl + "addFlagforPrint",
                type: "post",
                async: false,
                dataType: "jsonp", //指定服务器返回的数据类型
                data: {
                    seg_no: seg_no,
                    allocate_vehicle_id: print_allocate_vehicle_id
                },
                success: function(res) {

                }
            });
        }
    </script>

</html>