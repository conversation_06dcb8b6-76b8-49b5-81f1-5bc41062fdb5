package Controllers;

import java.awt.print.Book;
import java.awt.print.PageFormat;
import java.awt.print.Paper;
import java.awt.print.PrinterJob;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

import javax.print.Doc;
import javax.print.DocFlavor;
import javax.print.DocPrintJob;
import javax.print.PrintService;
import javax.print.PrintServiceLookup;
import javax.print.ServiceUI;
import javax.print.SimpleDoc;
import javax.print.attribute.DocAttributeSet;
import javax.print.attribute.HashDocAttributeSet;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.standard.Sides;
import javax.servlet.http.HttpServlet;
import javax.swing.JFileChooser;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.printing.PDFPageable;
import org.apache.pdfbox.printing.PDFPrintable;
import org.apache.pdfbox.printing.Scaling;
import org.json.JSONArray;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

@RestController
public class PrintController extends HttpServlet {

    @RequestMapping(value = "/ytjPrintIndex")
    public ModelAndView ytjPrintIndex() {
        ModelAndView mvAndView = new ModelAndView();
        mvAndView.setViewName("/WEB-INF/html/printIndex.html");
        return mvAndView;
    }

    @RequestMapping(value = "/ytjPrintPage")
    public ModelAndView ytjPrintPage() {
        ModelAndView mvAndView = new ModelAndView();
        mvAndView.setViewName("/WEB-INF/html/printPage.html");
        return mvAndView;
    }

    @RequestMapping(value = "/billPrintInput")
    public ModelAndView billPrintInput() {
        ModelAndView mvAndView = new ModelAndView();
        mvAndView.setViewName("/WEB-INF/html/billPrintInput.html");
        return mvAndView;
    }

    @RequestMapping(value = "/billPrintQuery")
    public ModelAndView billPrintQuery() {
        ModelAndView mvAndView = new ModelAndView();
        mvAndView.setViewName("/WEB-INF/html/billPrintQuery.html");
        return mvAndView;
    }

    /**
     * 本地处理打印pdf
     *
     * @throws InvalidPasswordException
     * @throws IOException
     */
    @RequestMapping(value = "/doPrintLocal")
    public String doPrintLocal(@RequestParam(value = "addrLink", required = false) String addrLink,
                               @RequestParam(value = "seg_no", required = false) String seg_no) {

        PDDocument document = null;

        try {
            // 下载报表pdf到本地
            int copies = 1;
            if ("00181".equals(seg_no)) {
                copies = 4;
            }
            return doPrint(addrLink, copies);
//            String downloadFlag = PDFDownload(addrLink);
//            URL url = new URL(addrLink);
//            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//            document = PDDocument.load(connection.getInputStream());
//            // System.out.println("okokokokokokokokokokok");
//            // String pdfPath = "D:/print/printDoc.pdf";
//            // InputStream file = new FileInputStream(document);
//            // File file = new File(pdfPath);
//            // 读取pdf文件
//            // PDDocument document = PDDocument.load(file);
//
//            // 使用打印机的名称
//            //String printName = "NPI7401F0 (HP LaserJet P2055dn)";
//            // String printName ="HP LaserJet 5200 PCL6 Class Driver";
//            String printName = "ytjprinter";
//            /*
//             * PrintPDF.main(new String[] { "-silentPrint", // 静默打印 "-printerName",
//             * printName, // 指定打印机名 "-orientation", "auto", // 打印方向，三种可选 pdfPath//
//             * 打印PDF文档的路径 });
//             */
//            // 创建打印任务
//            PrinterJob job = PrinterJob.getPrinterJob();
//            // 遍历所有打印机的名称
//            for (PrintService ps : PrinterJob.lookupPrintServices()) {
//                String psName = ps.toString();
//                // 选用指定打印机
//                if (psName.equals(printName)) { // isChoose = true;
//                    job.setPrintService(ps);
//                    break;
//                }
//            }
//
//            job.setPageable(new PDFPageable(document));
//
//            Paper paper = new Paper(); // 设置打印纸张大小 //
////            paper.setSize(598, 842); // 1/72 inch // 默认为A4纸张，对应像素宽和高分别为 595, 848
//            int width = 595;
//            int height = 842;
//            paper.setSize(width, height); // 设置打印位置 坐标 // 设置边距，单位是像素，10mm边距，对应 28px
//            int marginLeft = 10;
//            int marginRight = 0;
//            int marginTop = 10;
//            int marginBottom = 0;
//            paper.setImageableArea(marginLeft, marginRight, width - (marginLeft + marginRight),
//                    height - (marginTop + marginBottom));
//            // custom page format
//            PageFormat pageFormat = new PageFormat();
//            pageFormat.setPaper(paper);
//            // override the page format
//            Book book = new Book();
//            // append all pages 设置一些属性是否缩放 打印张数等 //// 实际大小 ACTUAL_SIZE,// 缩小SHRINK_TO_FIT,//
//            // 拉伸STRETCH_TO_FIT,//适应SCALE_TO_FIT;
//            book.append(new PDFPrintable(document, Scaling.ACTUAL_SIZE), pageFormat, document.getNumberOfPages());
//            pageFormat.setOrientation(PageFormat.LANDSCAPE);// 横向 job.setPageable(book);
//            // 开始打印
//            job.print();
//            document.close();
        } catch (Exception e) {
            e.printStackTrace();
            return "0";
        }
//        return "1";
    }

    // 将报表pdf下载到本地
    private String PDFDownload(String addrLink) throws Exception {
        // 将报表地址转为url
        URL url = new URL(addrLink);
        URLConnection URLconnection = url.openConnection();
        HttpURLConnection httpConnection = (HttpURLConnection) URLconnection;
        int responseCode = httpConnection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream in = httpConnection.getInputStream();

            // 存放司机报表文件路径
            String pathName = "D:/print/printDoc.pdf";
            String path = "D:/print/";
            // 判断路径是否存在
            if (path != null && !"".equals(path)) {
                File f = new File(path);
                if (!f.exists()) {// 目录不存在 创建目录
                    f.mkdirs();
                }
            }

            // 写出文件位置
            OutputStream out = new FileOutputStream(pathName);
            // 判断输入或输出是否准备好
            if (in != null && out != null) {
                int temp = 0;
                // 开始拷贝
                while ((temp = in.read()) != -1) {
                    // 边读边写
                    out.write(temp);
                }
                // 关闭输入输出流
                in.close();
                out.close();
            } else {
                System.out.println("》》》》》》》》》》》》失败");
            }
        }
        return "1";
    }

    @RequestMapping(value = "/doBillReportPrint")
    public String doBillReportPrint(String billReportUrls) {
        if (null == billReportUrls || "".equals(billReportUrls)) {
            return "0";
        }
        boolean printResult = true;
        try {
            JSONArray urlArray = new JSONArray(billReportUrls);
            for (int i = 0; i < urlArray.length(); i++) {
                String url = urlArray.getString(i);
                String result = doPrint(url, 1);
                if ("0".equals(result)) {
                    printResult = false;
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            printResult = false;
        }
        return printResult ? "1" : "0";
    }

    private String doPrint(String filePath, int copies) {
        PDDocument document = null;
        System.out.println("====>打印报表：" + filePath);
//        filePath = "http://10.30.91.38:7001/sree/Examples?op=Replet&outtype=PDF&name=stms/putout_print_batch_xs_hz&seg_no=00106&print_bat_id=**********&provider_bill_num=EL2109010014&user_id=dev";
        try {
            // 下载报表pdf到本地
            URL url = new URL(filePath);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            document = PDDocument.load(connection.getInputStream());
            // System.out.println("okokokokokokokokokokok");
            // String pdfPath = "D:/print/printDoc.pdf";
            // InputStream file = new FileInputStream(document);
            // File file = new File(pdfPath);
            // 读取pdf文件
            // PDDocument document = PDDocument.load(file);

            // 使用打印机的名称
            //String printName = "NPI7401F0 (HP LaserJet P2055dn)";
            // String printName ="HP LaserJet 5200 PCL6 Class Driver";
            String printName = "ytjprinter";
            // 创建打印任务
            PrinterJob job = PrinterJob.getPrinterJob();
            // 遍历所有打印机的名称
            for (PrintService ps : PrinterJob.lookupPrintServices()) {
                String psName = ps.toString();
                // 选用指定打印机
                if (psName.equals(printName)) { // isChoose = true;
                    job.setPrintService(ps);
                    break;
                }
            }
            job.setPageable(new PDFPageable(document));
            Paper paper = new Paper(); // 设置打印纸张大小 //
            double width = toPx(210);
            double height = toPx(297);
            paper.setSize(width, height); // 设置打印位置 坐标 // 设置边距，单位是像素，10mm边距，对应 28px
            double marginLeft = toPx(10);
            double marginRight = toPx(10);
            double marginTop = toPx(10);
            double marginBottom = toPx(10);
            paper.setImageableArea(marginLeft, marginRight, width - (marginLeft + marginRight),
                    height - (marginTop + marginBottom));
            // custom page format
            PageFormat pageFormat = new PageFormat();
            pageFormat.setOrientation(PageFormat.LANDSCAPE);// 横向 job.setPageable(book);
            pageFormat.setPaper(paper);
            // override the page format
            Book book = new Book();
            // append all pages 设置一些属性是否缩放 打印张数等 //// 实际大小 ACTUAL_SIZE,// 缩小SHRINK_TO_FIT,//
            // 拉伸STRETCH_TO_FIT,//适应SCALE_TO_FIT;
            for (int i = 1; i <= document.getNumberOfPages(); i++) {
                book.append(new PDFPrintable(document, Scaling.SCALE_TO_FIT), pageFormat, i);
//            book.append(new PDFPrintable(document, Scaling.SCALE_TO_FIT), pageFormat, document.getNumberOfPages());
            }
            job.setPageable(book);
            //打印份数
            job.setCopies(copies);//设置打印份数
            // 开始打印
            job.print();
            document.close();
        } catch (Exception e) {
            e.printStackTrace();
            return "0";
        }
        return "1";
    }

    double toPx(double num) {
        return num * 72 * 10 / 254;
    }

}
