<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
http://www.springframework.org/schema/context
http://www.springframework.org/schema/context/spring-context-3.1.xsd
http://www.springframework.org/schema/mvc
http://www.springframework.org/schema/mvc/spring-mvc-3.1.xsd">

	<!-- 启动注解驱动的Spring MVC功能，注册请求url和注解POJO类方法的映射 -->
	<mvc:annotation-driven>
		<!-- 消息转换器 -->
		<mvc:message-converters register-defaults="true">
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<property name="supportedMediaTypes" value="text/html;charset=GBK" />
			</bean>
		</mvc:message-converters>

	</mvc:annotation-driven>

	<mvc:default-servlet-handler />

	<!-- 启动包扫描功能，以便注册带有@Controllers、@service、@repository、@Component等注解的类成为spring的bean -->
	<context:component-scan base-package="Controllers" />
	<!-- 对模型视图名称的解析，在请求时模型视图名称添加前后缀 -->
	<bean
		class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="viewClass"
			value="org.springframework.web.servlet.view.JstlView" />
		<property name="prefix" value="/" />	<!-- 前缀 -->
		<property name="suffix" value="" />	<!-- 后缀 -->
	</bean>
	<!-- 访问静态文件（jpg,js,css）的方法 -->
	<!-- <mvc:resources location="/files/" mapping="/files/**" /> <mvc:resources 
		location="/scripts/" mapping="/scripts/**" /> <mvc:resources location="/styles/" 
		mapping="/styles/**" /> <mvc:resources location="/Views/" mapping="/Views/**" 
		/> -->
	<mvc:resources location="/WEB-INF/images/" mapping="/images/**" />
	<mvc:resources location="/WEB-INF/js/" mapping="/js/**" />
	<mvc:resources location="/WEB-INF/css/" mapping="/css/**" />
</beans>