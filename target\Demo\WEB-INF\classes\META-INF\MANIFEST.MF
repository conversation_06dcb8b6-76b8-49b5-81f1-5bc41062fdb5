Manifest-Version: 1.0
Class-Path: jsqlparser-0.9.1.jar stax2-api-3.1.4.jar spring-orm-4.3.5.
 RELEASE.jar spring-expression-4.3.5.RELEASE.jar commons-logging-1.2.j
 ar neethi-3.0.3.jar spring-web-4.3.1.RELEASE.jar jstl-1.2.jar commons
 -beanutils-1.8.3.jar spring-tx-4.3.5.RELEASE.jar c3p0-0.9.1.2.jar jso
 n-20160212.jar spring-beans-4.3.5.RELEASE.jar gson-2.6.2.jar spring-a
 op-4.3.5.RELEASE.jar fontbox-2.0.12.jar cxf-rt-bindings-soap-2.7.12.j
 ar pdfbox-app-2.0.16.jar commons-codec-1.10.jar xmlschema-core-2.1.0.
 jar jaxb-impl-2.1.13.jar commons-collections-3.2.1.jar spring-context
 -support-4.3.5.RELEASE.jar cxf-rt-ws-policy-2.7.12.jar xml-resolver-1
 .2.jar cxf-rt-core-2.7.12.jar spring-core-4.3.5.RELEASE.jar mysql-con
 nector-java-5.1.38.jar log4j-api-2.6.2.jar spring-webmvc-4.3.1.RELEAS
 E.jar commons-fileupload-1.3.1.jar fastjson-1.2.12.jar ezmorph-1.0.6.
 jar commons-lang-2.6.jar cxf-rt-bindings-xml-2.7.12.jar asm-3.3.1.jar
  commons-io-2.4.jar spring-mock-2.0.8.jar log4j-core-2.6.2.jar wsdl4j
 -1.6.3.jar annotations-java5-20.1.0.jar pagehelper-3.7.3.jar spring-j
 dbc-4.3.5.RELEASE.jar cxf-rt-frontend-simple-2.7.12.jar base64-2.3.8.
 jar jna-4.1.0.jar mybatis-3.3.0.jar woodstox-core-asl-4.4.0.jar geron
 imo-javamail_1.4_spec-1.7.1.jar mybatis-spring-1.2.3.jar spring-test-
 4.3.5.RELEASE.jar cxf-rt-databinding-jaxb-2.7.12.jar cxf-rt-ws-addr-2
 .7.12.jar cxf-rt-frontend-jaxws-2.7.12.jar cxf-api-2.7.12.jar spring-
 context-4.3.5.RELEASE.jar

