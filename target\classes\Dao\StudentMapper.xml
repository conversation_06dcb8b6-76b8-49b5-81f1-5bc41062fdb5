<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Dao.StudentMapper">
    <resultMap id="BaseResultMap" type="Entity.Student">
        <id column="Uid" jdbcType="BINARY" property="uid" />
        <result column="Name" jdbcType="VARCHAR" property="name" />
        <result column="Age" jdbcType="INTEGER" property="age" />
        <result column="ClassId" jdbcType="INTEGER" property="classid" />
    </resultMap>
    <sql id="Base_Column_List">
        Uid, Name, Age, ClassId
    </sql>
    <select id="selectByPrimaryKey" parameterType="byte[]" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from student
        where Uid = #{uid,jdbcType=BINARY}
    </select>
    <select id="selectByCondition" parameterType="Entity.Student" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from student
        <where>
            1=1
            <if test="uid != null">
                and Uid=#{uid,jdbcType=BINARY}
            </if>
            <if test="name != null">
                and Name=#{name,jdbcType=VARCHAR}
            </if>
            <if test="age != null">
                and Age=#{age,jdbcType=INTEGER}
            </if>
            <if test="classid != null">
                and ClassId=#{classid,jdbcType=INTEGER}
            </if>
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="byte[]">
        delete from student
        where Uid = #{uid,jdbcType=BINARY}
    </delete>
    <insert id="insert" parameterType="Entity.Student">
        insert into student (Uid, Name, Age,
        ClassId)
        values (#{uid,jdbcType=BINARY}, #{name,jdbcType=VARCHAR}, #{age,jdbcType=INTEGER},
        #{classid,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="Entity.Student">
        insert into student
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">
                Uid,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="age != null">
                Age,
            </if>
            <if test="classid != null">
                ClassId,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">
                #{uid,jdbcType=BINARY},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                #{age,jdbcType=INTEGER},
            </if>
            <if test="classid != null">
                #{classid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="Entity.Student">
        update student
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                Age = #{age,jdbcType=INTEGER},
            </if>
            <if test="classid != null">
                ClassId = #{classid,jdbcType=INTEGER},
            </if>
        </set>
        where Uid = #{uid,jdbcType=BINARY}
    </update>
    <update id="updateByPrimaryKey" parameterType="Entity.Student">
        update student
        set Name = #{name,jdbcType=VARCHAR},
        Age = #{age,jdbcType=INTEGER},
        ClassId = #{classid,jdbcType=INTEGER}
        where Uid = #{uid,jdbcType=BINARY}
    </update>
</mapper>