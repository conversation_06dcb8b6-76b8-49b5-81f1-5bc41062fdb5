package Controllers;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.sun.jna.Library;
import com.sun.jna.Native;

import Entity.ResultInfo;
import Service.IUserTestService;

@RestController
public class RequestTestController extends HttpServlet {

	@Autowired
	private IUserTestService service;

	// 调用Dll层接口
	public interface Dll extends Library {
		// String path=Dll.class.getResource("/").getPath()+"jna/BY618DLL.dll";
		String path = Dll.class.getResource("/").getPath().replaceFirst("/", "") + "jna/BY618DLL.dll";

		// String path = "/jna/BY618DLL.dll";

		// Dll.class.getResource("").getPath().replaceFirst("/","").replaceAll("%20"," ")+"BY618DLL.dll";
		// Dll instance = (Dll)Native.loadLibrary("D:/workspace/bep/bep-service/src/main/resources/jna/BY618DLL.dll", Dll.class);
		Dll instance = (Dll) Native.loadLibrary(path.replaceAll("%20", " "), Dll.class);

		// 初始化通讯端口
		public int SNBC_InitComm(int Icomm);

		// 卡认证操作
		public int SNBC_Authenticate();

		// 读卡操作
		public int SNBC_ReadContent();

		// 保存图片
		public int SNBC_SavePhoto(String str);

		// 获取姓名信息
		public int SNBC_GetName(byte[] str, int size);

		// 获取性别信息
		public int SNBC_GetSex(byte[] str, int size);

		// 获取民族信息
		public int SNBC_GetNation(byte[] str, int size);

		// 获取出生信息
		public int SNBC_GetBirth(byte[] str, int size);

		// 获取住址信息
		public int SNBC_GetAddress(byte[] str, int size);

		// 获取身份证号码信息字符串的缓冲区，应该大于36字节
		public int SNBC_GetID(byte[] str, int size);

		// 获取身份证卡的类型
		public void SNBC_GetidCardType(byte[] str, int size);

		// 关闭通讯端口
		public int SNBC_CloseComm(int iComm);

	}

	// 返回首页
	@RequestMapping(value = "/nextStep")
	public ModelAndView nextStep() {
		ModelAndView mvAndView = new ModelAndView();
		System.out.println("123123");
		mvAndView.setViewName("/WEB-INF/html/intoFactory.html");
		return mvAndView;
	}

	// 开启端口
	@RequestMapping(value = "/ReadIdCard")
	@ResponseBody
	public ResultInfo initCoinitCommmm() {
		System.out.println(Dll.class.getResource("/").getPath().replaceFirst("/", "") + "jna/BY618DLL.dll");
		System.out.println("------------------initComm------------------");
		// TODO mac启动需注释掉dll的初始化加载
		 int snbc_InitComm = Dll.instance.SNBC_InitComm(1001);
		if (0 != snbc_InitComm) {
			return new ResultInfo("0", "读卡失败,请重新放置身份证!", "USB口打开失败");
		}
		int snbc_Authenticate = Dll.instance.SNBC_Authenticate();
		if (0 != snbc_Authenticate) {
			return new ResultInfo("0", "读卡失败,请重新放置身份证!", "卡认证失败!");
		}
		int snbc_ReadContent = Dll.instance.SNBC_ReadContent();
		if (0 != snbc_ReadContent) {
			return new ResultInfo("0", "读卡失败,请重新放置身份证!", "读卡操作失败!");
		}
		return new ResultInfo("1", "读卡操作成功!", "读卡操作成功!");
	}

	// 获取信息
	@RequestMapping(value = "/GetIdCard")
	@ResponseBody
	public ResultInfo getIdentityInfo() {
		HashMap<String, String> param = new HashMap<String, String>();

		byte str1[] = new byte[31];
		int iRtn = Dll.instance.SNBC_GetName(str1, 31);

		// ID
		byte str2[] = new byte[37];
		iRtn = Dll.instance.SNBC_GetID(str2, 37);
		try {
			String strName = new String(str1, "GBK").trim();
			String strID = new String(str2, "GBK").trim();

			param.put("strName", strName);
			param.put("strID", strID);
		} catch (Exception e) {
			System.err.println("读卡操作失败!(02602)");
			return new ResultInfo("0", "读卡操作失败!", e);
		}

		return new ResultInfo("1", "读卡操作成功!", param, "读卡操作成功!");
	}

	// 关闭端口
	@RequestMapping(value = "/CloseIdCard")
	@ResponseBody
	public ResultInfo closeComm() {
		int snbc_CloseComm = Dll.instance.SNBC_CloseComm(1001);
		if (0 != snbc_CloseComm) {
			return new ResultInfo("0", "读卡失败,请重新放置身份证!", "USB口关闭失败!");
		}
		return new ResultInfo("1", "读卡操作成功!", "读卡操作成功!");
	}

	// 首页访问
	@RequestMapping(value = "/registerHome")
	public ModelAndView TestString(@RequestParam String seg_no) {
		ModelAndView mvAndView = new ModelAndView();
		mvAndView.setViewName("/WEB-INF/html/selfServiceMachine.html");
		System.out.println(seg_no);
		mvAndView.addObject("seg_no",seg_no);
		return mvAndView;
	}

	// 首页-下一步
	@RequestMapping(value = "/next")
	public ModelAndView next() {

		ModelAndView mvAndView = new ModelAndView();
		mvAndView.setViewName("/WEB-INF/html/selfServiceMachineNext.html");

		return mvAndView;
	}

	// 装卸类型选择->扫描提单（上海不锈）
	@RequestMapping(value = "/billScan")
	public ModelAndView billScan() {

		ModelAndView mvAndView = new ModelAndView();
		mvAndView.setViewName("/WEB-INF/html/billScan.html");

		return mvAndView;
	}

	// 一体机首页 入场登记//自助打印
	@RequestMapping(value = "/ytjindex")
	public ModelAndView ytjindex() {

		ModelAndView mvAndView = new ModelAndView();
		mvAndView.setViewName("/WEB-INF/html/ytjindex.html");
		return mvAndView;
	}

}
