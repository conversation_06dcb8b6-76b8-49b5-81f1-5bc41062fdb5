@charset "utf-8";
/* CSS Document */
body {
	margin: 0;
	padding: 0;
	font-size: 24px;
	font-family: Microsoft Yahei, 微软雅黑, sans-serif;
}

a {
	text-decoration: none;
}

ul li {
	margin: 0px;
	padding: 0px;
	list-style: none;
}

a {
	color: #666;
}

p {
	padding: 0;
	margin: 0;
}
/************************information begin*/
.wrapper {
	width: 100%;
	height: 80%;
	background: #ffffff;
}

.main {
	width: 1170px;
	margin: 0 auto;
}

.information {
	width: 60%;
	height: 240px;
	float: left;
	margin-top: 20px;
	margin-left: -40px;
}

.information ul li {
	margin-bottom: 20px;
}

.information span {
	float: left;
	width: 84px;
	height: 50px;
	line-height: 50px;
	margin-right: 10px;
	display: block;
}

.ipt::-webkit-input-placeholder {
	color: #999;
	font-size: 24px;
	font-weight: 300;
	font-family: Microsoft Yahei, sans-serif;
}

.ipt::-moz-placeholder {
	color: #999;
	font-size: 24px;
	font-weight: 300;
	font-family: Microsoft Yahei, sans-serif;
}

.ipt::-moz-placeholder {
	color: #999;
	font-size: 24px;
	font-weight: 300;
	font-family: Microsoft Yahei, sans-serif;
}

.ipt:-ms-input-placeholder {
	color: #999;
	font-size: 24px;
	font-weight: 300;
	font-family: Microsoft Yahei, sans-serif;
}

input {
	width: 430px;
	height: 50px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(102, 102, 102, 1);
	border-radius: 4px;
	font-size: 28px;
	text-align: left;
	text-indent: 10px;
	outline: none;
}

.btn {
	width: 220px;
	height: 180px;
	float: right;
	margin-top: 45px;
	margin-right: 5px;
}

button {
	width: 220px;
	height: 80px;
	background: linear-gradient(180deg, rgba(28, 147, 191, 1),
		rgba(28, 147, 191, 1), rgba(28, 147, 191, 1), rgba(22, 131, 171, 1));
	border-radius: 4px;
	border: none;
	font-size: 24px;
	color: #fff;
	text-indent: 10px;
	font-family: Microsoft Yahei, sans-serif;
}

.next {
	margin-top: 20px;
}


.keyboard {
	width: 100%;
	height: 270px;
	background-color: #e5ebf3;
	position: absolute;
	bottom: 0px;
	padding: 0px;
}

.number {
	width: 1260px;
	height: 240px;
	margin: 0 auto;
	margin-top: -18px;
}

.number ul li {
	float: left;
	width: 106px;
	height: 50px;
	line-height: 50px;
	background: #fff;
	border: solid #d2d2d2 1px;
	border-radius: 4px;
	box-shadow: 2px 2px 4px rgba(102, 102, 102, 0.16);
	text-align: center;
	margin-top: 10px;
	margin-right: 10px;
}
/************************information end*/
/************************information2 begin*/
#province {
	width: 80px;
	height: 50px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(102, 102, 102, 1);
	border-radius: 4px;
}

#license {
	width: 335px;
	height: 50px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(102, 102, 102, 1);
	border-radius: 4px;
}

.loading {
	width: 210px;
	height: 60px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(210, 210, 210, 1);
	box-shadow: 0px 3px 3px 0px rgba(102, 102, 102, 0.16);
	border-radius: 4px;
	color: #333;
}

.discharge {
	width: 210px;
	height: 60px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(210, 210, 210, 1);
	box-shadow: 0px 3px 3px 0px rgba(102, 102, 102, 0.16);
	border-radius: 4px;
	margin-left: 10px;
	color: #333;
}
/************************information2 end*/
/************************information3 begin*/
.tips {
	width: 1170px;
	height: 30px;
	margin: 40px auto 10px;
}

.tips p {
	display: block;
	text-align: right;
	margin-right: -10px;
	font-size: 24px;
	font-weight: 400;
	color: #ecbd3e;
	line-height: 36px;
}

.checked {
	width: 210px;
	height: 60px;
	background: #ecbd3e;
	box-shadow: 4px 4px 4px 0px #ae8a2a inset;
	border-radius: 4px;
}

.unchecked {
	width: 210px;
	height: 60px;
	background: #e8e8e8;
	border: 1px solid #d2d2d2;
	border-radius: 4px;
	margin-left: 10px;
	color: #999;
}
/************************information3 end*/
/************************informationcheck begin*/
.ibox {
	width: 600px;
	height: 310px;
	float: left;
	margin-top: 10px;
	margin-left: 0px;
	background: #f5f5f5;
	border: 1px solid #d2d2d2;
	border-radius: 4px;
}

i {
	font-style: normal;
	color: #666;
}

.ibox p {
	padding: 10px;
}
/************************informationcheck end*/